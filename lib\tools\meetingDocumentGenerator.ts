/**
 * Meeting Document Generation Tool for PMO Agents
 * 
 * This tool enables PMO Agents to create new documents during meetings
 * based on discussion content and web search results.
 */

import { processWithGoogleAI } from './google-ai';
import { processPMODocument } from '../pmo/processPMODocument';
import { uploadToKnowledgeBaseWithDeduplication } from '../../components/scriptreaderAI/uploadKnowledgebase';
import { TranscriptMessage } from '../../components/PMO/TranscriptPanel';
import { v4 as uuidv4 } from 'uuid';

export interface WebSearchResult {
  title?: string;
  url?: string;
  snippet?: string;
  description?: string;
}

export interface MeetingDocumentGenerationOptions {
  title: string;
  category: string;
  meetingTranscript: TranscriptMessage[];
  agentId?: string;
  userId: string;
  webSearchResults?: WebSearchResult[];
  additionalContext?: string;
  pmoId?: string; // Optional PMO ID to link the document to a PMO record
  documentId?: string; // Optional document ID to use instead of generating new one
  onProgress?: (step: string, message: string, progress?: number) => void;
}

export interface MeetingDocumentGenerationResult {
  success: boolean;
  documentId?: string;
  downloadUrl?: string;
  fileName?: string;
  knowledgeBaseId?: string;
  error?: string;
}

/**
 * Generate a document based on meeting discussion and context
 */
export async function generateMeetingDocument(
  options: MeetingDocumentGenerationOptions
): Promise<MeetingDocumentGenerationResult> {
  try {
    console.log('[MEETING_DOC_GEN] Starting document generation:', {
      title: options.title,
      category: options.category,
      transcriptLength: options.meetingTranscript.length,
      hasWebSearch: !!options.webSearchResults?.length,
      userId: options.userId
    });

    options.onProgress?.('start', 'Initializing document generation...', 0);

    // Extract meeting content and context
    const meetingContent = extractMeetingContent(options.meetingTranscript);
    const webSearchContext = formatWebSearchResults(options.webSearchResults || []);

    options.onProgress?.('analysis', 'Analyzing meeting content...', 20);

    // Generate summary using Gemini 2.5 Pro
    options.onProgress?.('summary', 'Generating meeting summary with Gemini 2.5 Pro...', 30);
    const meetingSummary = await generateMeetingSummary(options.meetingTranscript);

    options.onProgress?.('generation', 'Creating document structure...', 40);

    // Create document content with both full transcript and summary
    const generatedContent = createMeetingDocumentContent({
      title: options.title,
      category: options.category,
      meetingSummary,
      fullTranscript: meetingContent,
      webSearchContext,
      additionalContext: options.additionalContext
    });

    if (!generatedContent || generatedContent.trim().length === 0) {
      return {
        success: false,
        error: 'Failed to generate document content'
      };
    }

    console.log('[MEETING_DOC_GEN] Content generated successfully, length:', generatedContent.length);

    options.onProgress?.('processing', 'Processing and storing document...', 60);

    // Use provided PMO ID or generate a new one
    const pmoId = options.pmoId || uuidv4();

    // Process the document using existing PMO document processing
    const processResult = await processPMODocument({
      title: options.title,
      content: generatedContent,
      category: options.category,
      pmoId,
      userId: options.userId, // Add required userId parameter
      agentId: options.agentId, // Pass agentId for files metadata
      documentId: options.documentId, // Use provided document ID if available
      useUserPath: true, // Use user's path instead of sysAdmin
      metadata: {
        source: 'agent-generated',
        generatedBy: 'PMOAgent',
        agentId: options.agentId,
        generatedAt: new Date().toISOString(),
        meetingTranscriptLength: options.meetingTranscript.length.toString(),
        hasWebSearchResults: ((options.webSearchResults?.length || 0) > 0).toString(),
        documentType: 'Meeting-Generated Document'
      }
    });

    if (!processResult.success) {
      return {
        success: false,
        error: `Failed to process document: ${processResult.error}`
      };
    }

    console.log('[MEETING_DOC_GEN] Document processed successfully:', {
      documentId: processResult.documentId,
      downloadUrl: processResult.downloadUrl
    });

    // Generate fileName for the document
    const sanitizedTitle = options.title.replace(/[^a-zA-Z0-9_-]/g, '_').substring(0, 50);
    const fileName = `PMO_Meeting_Document_${sanitizedTitle}_${pmoId}.pdf`;

    let knowledgeBaseId: string | undefined;

    // Upload to ElevenLabs Knowledge Base if agent ID is provided
    if (options.agentId && processResult.downloadUrl) {
      try {
        options.onProgress?.('upload', 'Uploading to agent knowledge base...', 80);

        console.log('[MEETING_DOC_GEN] Uploading to knowledge base for agent:', options.agentId);

        const uploadResult = await uploadToKnowledgeBaseWithDeduplication(
          processResult.downloadUrl,
          fileName,
          'application/pdf',
          process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY || process.env.NEXT_PUBLIC_ELEVENLABS_COMPANY_API_KEY || '',
          false // Don't force upload, use deduplication
        );

        if (uploadResult && uploadResult.id) {
          knowledgeBaseId = uploadResult.id;
          console.log('[MEETING_DOC_GEN] Successfully uploaded to knowledge base:', knowledgeBaseId);
        }
      } catch (kbError) {
        console.error('[MEETING_DOC_GEN] Knowledge base upload failed:', kbError);
        // Don't fail the entire operation if KB upload fails
      }
    }

    options.onProgress?.('complete', 'Document generation completed successfully!', 100);

    return {
      success: true,
      documentId: processResult.documentId,
      downloadUrl: processResult.downloadUrl,
      fileName: fileName,
      knowledgeBaseId
    };

  } catch (error) {
    console.error('[MEETING_DOC_GEN] Error generating meeting document:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Extract meaningful content from meeting transcript
 */
function extractMeetingContent(transcript: TranscriptMessage[]): string {
  if (!transcript || transcript.length === 0) {
    return 'No meeting content available.';
  }

  const formattedMessages = transcript.map(message => {
    const timestamp = message.timestamp.toLocaleTimeString();
    const speaker = message.role === 'user' ? 'User' : 'Agent';
    return `[${timestamp}] ${speaker}: ${message.content}`;
  }).join('\n\n');

  return formattedMessages;
}

/**
 * Generate a summary of the meeting transcript using Gemini 2.5 Pro
 */
async function generateMeetingSummary(transcript: TranscriptMessage[]): Promise<string> {
  if (!transcript || transcript.length === 0) {
    return 'No meeting content available for summarization.';
  }

  try {
    const fullTranscript = extractMeetingContent(transcript);

    const summaryPrompt = `Please provide a comprehensive summary of the following meeting transcript.

Structure your summary as follows:
1. **Meeting Overview**: Brief description of the meeting purpose and participants
2. **Key Discussion Points**: Detailed points discussed and include details of web research conducted during the meeting
3. **Decisions Made**: Any decisions or conclusions reached
4. **Action Items**: Tasks or follow-ups identified (if any)
5. **Next Steps**: Planned future actions or meetings

Meeting Transcript:
${fullTranscript}

Please provide a clear, professional summary that captures the essential information from this meeting.`;

    console.log('[MEETING_DOC_GEN] Generating summary with Gemini 2.5 Pro...');

    const summary = await processWithGoogleAI({
      prompt: summaryPrompt,
      model: 'gemini-2.5-pro'
    });

    return summary;
  } catch (error) {
    console.error('[MEETING_DOC_GEN] Error generating summary:', error);
    return 'Error generating meeting summary. Please refer to the full transcript below.';
  }
}

/**
 * Create structured meeting document content with both summary and full transcript
 */
function createMeetingDocumentContent(options: {
  title: string;
  category: string;
  meetingSummary: string;
  fullTranscript: string;
  webSearchContext: string;
  additionalContext?: string;
}): string {
  const currentDate = new Date().toLocaleDateString();
  const currentTime = new Date().toLocaleTimeString();

  let content = `# ${options.title}

**Document Type:** ${options.category}
**Generated:** ${currentDate} at ${currentTime}
**Generated By:** PMO Agent Meeting Documentation System

---

## Executive Summary

${options.meetingSummary}

---

## Full Meeting Transcript

The complete conversation transcript is provided below for detailed reference and record-keeping purposes.

${options.fullTranscript}`;

  // Add web search context if available
  if (options.webSearchContext && options.webSearchContext.trim().length > 0) {
    content += `

---

## Research & Web Search Results

${options.webSearchContext}`;
  }

  // Add additional context if available
  if (options.additionalContext && options.additionalContext.trim().length > 0) {
    content += `

---

## Additional Context & Notes

${options.additionalContext}`;
  }

  content += `

---

*This document was automatically generated by the PMO Agent Meeting transcrit System. The full transcript preserves the complete conversation for reference, while the executive summary provides key highlights and action items.*`;

  return content;
}

/**
 * Format web search results for context
 */
function formatWebSearchResults(results: WebSearchResult[]): string {
  if (!results || results.length === 0) {
    return '';
  }

  const formattedResults = results.map((result, index) => {
    return `${index + 1}. ${result.title || 'Untitled'}
   URL: ${result.url || 'No URL'}
   Summary: ${result.snippet || result.description || 'No description available'}`;
  }).join('\n\n');

  return `\n\nWeb Search Results:\n${formattedResults}`;
}

/**
 * Create a comprehensive prompt for document generation
 */
function createDocumentGenerationPrompt(options: {
  title: string;
  category: string;
  meetingContent: string;
  webSearchContext: string;
  additionalContext?: string;
}): string {
  return `You are a professional PMO (Project Management Office) document generator. Your task is to create a comprehensive, well-structured document based on a meeting discussion and additional context.

**Document Requirements:**
- Title: ${options.title}
- Category: ${options.category}
- Format: Professional business document with clear structure
- Length: Comprehensive but concise (aim for 2-4 pages when printed)

**Meeting Discussion Content:**
${options.meetingContent}

${options.webSearchContext}

${options.additionalContext ? `\n**Additional Context:**\n${options.additionalContext}` : ''}

**Instructions:**
1. Analyze the meeting discussion to identify key topics, decisions, action items or key findings from web search results
2. Create a professional document that captures the essence of the discussion
3. Structure the document with appropriate headings and sections
4. Include relevant insights from web search results if provided
5. Ensure the document is actionable and provides value for future reference
6. Use professional business language appropriate for PMO documentation

**Document Structure:**
- Executive Summary
- Key Discussion Points
- Key Findings
- Decisions Made (if applicable)
- Action Items and Next Steps (if applicable)
- Recommendations (if applicable)
- Appendices (if needed)

**Output Format:**
Provide the complete document in Markdown format with proper headings, bullet points, and formatting. The document should be ready for PDF conversion and professional presentation.

Generate the document now:`;
}

/**
 * Determine document category based on meeting context
 */
export function determineMeetingDocumentCategory(
  selectedDocumentCategories: string[],
  meetingContent: string
): string {
  // If there are selected document categories, use the most common one
  if (selectedDocumentCategories && selectedDocumentCategories.length > 0) {
    // Count category occurrences
    const categoryCount: Record<string, number> = {};
    selectedDocumentCategories.forEach(category => {
      categoryCount[category] = (categoryCount[category] || 0) + 1;
    });

    // Return the most frequent category
    const mostFrequentCategory = Object.entries(categoryCount)
      .sort(([,a], [,b]) => b - a)[0][0];
    
    return mostFrequentCategory;
  }

  // Fallback: analyze meeting content for category hints
  const contentLower = meetingContent.toLowerCase();
  
  if (contentLower.includes('strategy') || contentLower.includes('planning')) {
    return 'Strategic Planning';
  }
  if (contentLower.includes('requirement') || contentLower.includes('specification')) {
    return 'Requirements';
  }
  if (contentLower.includes('assessment') || contentLower.includes('analysis')) {
    return 'Assessment';
  }
  if (contentLower.includes('research') || contentLower.includes('investigation')) {
    return 'Research';
  }
  
  // Default category
  return 'General';
}
