import { Buffer } from 'node:buffer';
import { storageTool, SavePdfToByteStoreResult } from './storage-tool';
import { markdownRendererTool } from './markdown-renderer-tool';
import { ChartConfig, CHART_TYPES } from './chart-tool';

/**
 * Interface for PDF content
 */
export interface PdfContent {
  title: string;
  content: string;
}

/**
 * Interface for PDF generation options
 */
export interface PdfGenerationOptions {
  title?: string;
  subtitle?: string;
  date?: string;
  logoPath?: string;
  margin?: number;
  size?: string;
  includeCover?: boolean;
  includeToc?: boolean;
  saveToByteStore?: boolean;
  category?: string;
  agentId?: string;
  agentName?: string;
  contents?: PdfContent[];
  // Additional properties that might be used as metadata
  generatedAt?: string;
  generatedBy?: string;
  documentType?: string;
  tags?: string[];
  version?: string;
  author?: string;
  department?: string;
  status?: string;
  priority?: string;
  dueDate?: string;
  relatedDocuments?: string[];
  // Analytics report specific properties
  reportId?: string;
  reportType?: string;
  // Strategy specific properties
  strategyId?: string;
  // Research specific properties
  queryId?: string;
  subTaskId?: string;
}

/**
 * PDF Generator Tool for creating PDFs from formatted content
 */
export class PdfGeneratorTool {
  /**
   * Generate a PDF from formatted content
   * @param contents - Array of content objects with title and content properties
   * @param options - PDF generation options
   * @returns PDF buffer
   */
  async generatePdf(contents: PdfContent[], options: PdfGenerationOptions = {}): Promise<Buffer | SavePdfToByteStoreResult> {
    try {
      // Dynamic import for jsPDF to handle server-side environment
      const { jsPDF } = await import('jspdf');

      // Initialize jsPDF instance for PDF generation
      const doc = new jsPDF();

      // Set initial position
      let yPosition = 20;

      // Add cover page if requested
      if (options.includeCover !== false) {
        yPosition = this.addCoverPage(doc, contents, options, yPosition);
      }

      // Add table of contents if requested
      if (options.includeToc !== false) {
        doc.addPage();
        yPosition = 20;
        yPosition = this.addTableOfContents(doc, contents, options, yPosition);
      }

      // Add content pages
      for (const content of contents) {
        // Process markdown content using the MarkdownRendererTool
        const processedContent = {
          title: content.title,
          content: markdownRendererTool.markdownToPdfFormat(content.content)
        };

        doc.addPage();
        yPosition = 20;
        yPosition = this.addContentPage(doc, processedContent, options, yPosition);
      }

      // Convert PDF to ArrayBuffer and then to Node Buffer
      const pdfArrayBuffer = doc.output("arraybuffer");
      const pdfBuffer = Buffer.from(pdfArrayBuffer);

      // If saveToByteStore is true, save to byteStore
      if (options.saveToByteStore) {
        // Combine all content into a single string for vector search
        // Use plain text conversion for better vector search results
        const combinedContent = contents.map(content =>
          `${content.title}\n\n${markdownRendererTool.markdownToPlainText(content.content)}`
        ).join('\n\n');

        // Use the title from options or from the first content item
        const title = options.title || (contents.length > 0 ? contents[0].title : 'Untitled Document');

        // Save to byteStore with Marketing Agent Team category
        const category = options.category || 'Marketing Agent Team';

        // Add metadata
        const metadata: Record<string, string | string[] | undefined> = {
          generatedAt: new Date().toISOString(),
          generatedBy: options.agentName || 'Marketing Agent',
          agentId: options.agentId || 'unknown',
          documentType: options.documentType,
          author: options.author,
          department: options.department,
          status: options.status,
          priority: options.priority,
          dueDate: options.dueDate,
          version: options.version,
          reportId: options.reportId,
          reportType: options.reportType,
          strategyId: options.strategyId,
          queryId: options.queryId,
          subTaskId: options.subTaskId,
          tags: Array.isArray(options.tags) ? options.tags.join(',') : undefined,
          relatedDocuments: Array.isArray(options.relatedDocuments) ? options.relatedDocuments.join(',') : undefined
        };

        // Filter out undefined values
        const cleanedMetadata: Record<string, string> = {};
        Object.entries(metadata).forEach(([key, value]) => {
          if (value !== undefined) {
            cleanedMetadata[key] = String(value);
          }
        });

        // Save to byteStore
        // Use agentId from metadata as agentType for proper file metadata storage
        const agentTypeForStorage = cleanedMetadata.agentId || 'MarketingAgent';
        return await storageTool.savePdfToByteStore(
          pdfBuffer,
          title,
          combinedContent,
          category,
          cleanedMetadata,
          agentTypeForStorage
        );
      }

      // Return the PDF buffer if not saving to byteStore
      return pdfBuffer;
    } catch (error) {
      console.error('Error generating PDF:', error);
      throw error;
    }
  }

  /**
   * Add a cover page to the PDF
   * @private
   */
  private addCoverPage(doc: any, _contents: PdfContent[], options: PdfGenerationOptions, startY: number): number {
    let yPosition = startY;

    // Set title
    const title = options.title || "Generated Document";

    // Add title
    doc.setFontSize(18);
    doc.setFont("helvetica", "bold");
    const titleWidth = doc.getTextWidth(title);
    const pageWidth = doc.internal.pageSize.getWidth();
    doc.text(title, (pageWidth - titleWidth) / 2, yPosition);

    yPosition += 20;

    // Add subtitle if provided
    if (options.subtitle) {
      doc.setFontSize(14);
      doc.setFont("helvetica", "normal");
      const subtitleWidth = doc.getTextWidth(options.subtitle);
      doc.text(options.subtitle, (pageWidth - subtitleWidth) / 2, yPosition);
      yPosition += 10;
    }

    // Add date
    const date = options.date || new Date().toLocaleDateString();
    doc.setFontSize(12);
    const dateText = `Generated on: ${date}`;
    const dateWidth = doc.getTextWidth(dateText);
    doc.text(dateText, (pageWidth - dateWidth) / 2, yPosition);

    return yPosition;
  }

  /**
   * Add table of contents to the PDF
   * @private
   */
  private addTableOfContents(doc: any, contents: PdfContent[], _options: PdfGenerationOptions, startY: number): number {
    let yPosition = startY;

    // Add TOC title
    doc.setFontSize(11);
    doc.setFont("helvetica", "bold");
    const tocTitle = 'Table of Contents';
    const tocTitleWidth = doc.getTextWidth(tocTitle);
    const pageWidth = doc.internal.pageSize.getWidth();
    doc.text(tocTitle, (pageWidth - tocTitleWidth) / 2, yPosition);

    yPosition += 15;

    // Add each content item to TOC
    doc.setFontSize(11);
    doc.setFont("helvetica", "normal");

    contents.forEach((content, index) => {
      const tocEntry = `${index + 1}. ${content.title || 'Untitled Section'}`;
      doc.text(tocEntry, 20, yPosition);
      yPosition += 10;

      // Check if we need a new page
      if (yPosition > 280) {
        doc.addPage();
        yPosition = 20;
      }
    });

    return yPosition;
  }

  /**
   * Add a content page to the PDF
   * @private
   */
  private addContentPage(doc: any, content: PdfContent, _options: PdfGenerationOptions, startY: number): number {
    let yPosition = startY;

    // Add title
    doc.setFontSize(14);
    doc.setFont("helvetica", "bold");
    doc.text(content.title || 'Untitled Section', 20, yPosition);

    yPosition += 10;

    // Add content with improved markdown handling
    doc.setFontSize(10);
    doc.setFont("helvetica", "normal");

    // Check if content contains chart data
    if (content.content.includes('<chart-data>')) {
      // Extract chart data
      const chartDataMatch = content.content.match(/<chart-data>([\s\S]*?)<\/chart-data>/);
      if (chartDataMatch && chartDataMatch[1]) {
        try {
          // Parse the chart data
          const chartConfig = JSON.parse(chartDataMatch[1]) as ChartConfig;

          // Render the chart based on its type
          return this.renderChartInPdf(doc, chartConfig, content.title || 'Chart', yPosition);
        } catch (error) {
          console.error('Error processing chart data:', error);
          // Add a note about chart data error
          doc.text('Chart visualization could not be rendered due to an error.', 20, yPosition);
          yPosition += 10;
          doc.text('Falling back to text representation.', 20, yPosition);
          yPosition += 20;

          // Fall back to regular text processing
        }
      }
    }

    // Check if content contains HTML tags from our enhanced markdown renderer
    let newYPosition: number;
    if (content.content.includes('<h') || content.content.includes('<strong>') || content.content.includes('<em>')) {
      // Process HTML-formatted content
      newYPosition = this.addHtmlFormattedContent(doc, content.content, yPosition);
    } else {
      // Process regular markdown content line by line
      newYPosition = this.addPlainMarkdownContent(doc, content.content, yPosition);
    }
    return newYPosition;
  }

  /**
   * Process HTML-formatted content from the enhanced markdown renderer
   * @private
   */
  private addHtmlFormattedContent(doc: any, content: string, startY: number): number {
    let yPosition = startY;
    const contentLines = content.split('\n');

    // Track <pre> code block state across lines
    let inPre = false;
    let preBuffer: string[] = [];

    for (let i = 0; i < contentLines.length; i++) {
      const line = contentLines[i];

      // If we are inside a <pre> block, accumulate until we find the closing tag
      if (inPre) {
        preBuffer.push(line);
        if (line.includes('</pre>')) {
          // Render the accumulated pre block
          const preJoined = preBuffer.join('\n');
          const codeText = preJoined
            .replace(/<pre[^>]*>/, '')
            .replace(/<\/pre>/, '');

          // Draw background box for code
          const maxWidth = 170;
          const lines = doc.splitTextToSize(codeText, maxWidth);

          // Check for page overflow before drawing box
          if (yPosition + 10 + (lines.length * 6) > 290) {
            doc.addPage();
            yPosition = 20;
          }

          // Background
          doc.setFillColor(245, 245, 245);
          doc.setDrawColor(220, 220, 220);
          const boxHeight = Math.min(1000, Math.max(14, lines.length * 6 + 6));
          doc.roundedRect(18, yPosition - 4, 174, boxHeight, 2, 2, 'FD');

          // Code text
          doc.setFont('courier', 'normal');
          lines.forEach((t: string, idx: number) => {
            // Ensure we don't overflow the page mid-block
            if (yPosition > 285) {
              doc.addPage();
              yPosition = 20;
            }
            doc.text(t, 22, yPosition + (idx * 6));
          });
          // Advance y beyond the box
          yPosition += boxHeight + 6;
          // Reset font
          doc.setFont('helvetica', 'normal');

          // Reset state
          inPre = false;
          preBuffer = [];
        }
        continue; // Continue to next line while in <pre>
      }

      // Detect start of a <pre> block
      if (line.includes('<pre')) {
        inPre = true;
        preBuffer = [line];
        continue;
      }

      // Check for page overflow
      if (yPosition > 280) {
        doc.addPage();
        yPosition = 20;
      }

      // Handle heading tags
      if (line.match(/<h[1-6][^>]*>.*?<\/h[1-6]>/)) {
        const headingMatch = line.match(/<h([1-6])[^>]*>(.*?)<\/h[1-6]>/);
        if (headingMatch) {
          const level = parseInt(headingMatch[1]);
          const text = headingMatch[2];

          // Set font size based on heading level
          const fontSize = 20 - ((level - 1) * 2); // h1=20, h2=18, h3=16, etc.
          doc.setFontSize(fontSize);
          doc.setFont("helvetica", "bold");
          doc.text(text, 20, yPosition);
          doc.setFontSize(12); // Reset to default
          doc.setFont("helvetica", "normal");

          yPosition += 10;
          continue;
        }
      }

      // Handle bullet points FIRST to preserve full line content (labels and values)
      if (line.includes('•')) {
        const indentMatch = line.match(/^(\s*)•/);
        if (indentMatch) {
          const indentSize = indentMatch[1].length;
          const indent = 20 + (indentSize * 2);
          const textWithHtml = line.replace(/^\s*•\s*/, '');
          // Strip HTML tags like <strong>, <em>, <a>, <code>, etc., keeping inner text
          const plainText = textWithHtml.replace(/<[^>]*>/g, '');

          // Split text to fit page width with indentation
          const textLines = doc.splitTextToSize(plainText, 170 - (indent - 20));

          textLines.forEach((textLine: string) => {
            doc.text(textLine, indent, yPosition);
            yPosition += 7;

            if (yPosition > 280) {
              doc.addPage();
              yPosition = 20;
            }
          });

          continue;
        }
      }

      // Fallback: handle markdown list lines that weren't converted to '•'
      if (line.match(/^(\s*)-\s+/) || line.match(/^(\s*)\d+\.\s+/)) {
        const hyphenMatch = line.match(/^(\s*)-\s+(.*)$/);
        const numberedMatch = line.match(/^(\s*)\d+\.\s+(.*)$/);
        const indentSize = (hyphenMatch ? hyphenMatch[1].length : (numberedMatch ? numberedMatch[1].length : 0));
        const indent = 20 + (indentSize * 2);
        const contentPart = (hyphenMatch ? hyphenMatch[2] : (numberedMatch ? numberedMatch[2] : line));
        const plainText = contentPart.replace(/<[^>]*>/g, '');

        const textLines = doc.splitTextToSize(plainText, 170 - (indent - 20));
        textLines.forEach((textLine: string) => {
          doc.text(textLine, indent, yPosition);
          yPosition += 7;
          if (yPosition > 280) { doc.addPage(); yPosition = 20; }
        });
        continue;
      }

      // Handle strong/bold-only lines (standalone emphasis)
      if (line.includes('<strong>') && !line.includes('•')) {
        // Extract text between strong tags
        const boldMatch = line.match(/<strong>(.*?)<\/strong>/);
        if (boldMatch) {
          doc.setFont("helvetica", "bold");
          const text = boldMatch[1];
          doc.text(text, 20, yPosition);
          doc.setFont("helvetica", "normal");
          yPosition += 7;
          continue;
        }
      }

      // Handle blockquotes
      if (line.includes('<blockquote')) {
        const quoteMatch = line.match(/<blockquote[^>]*>(.*?)<\/blockquote>/);
        if (quoteMatch) {
          const text = quoteMatch[1];

          // Draw a line for the blockquote
          doc.setDrawColor(200, 200, 200);
          doc.line(15, yPosition - 5, 15, yPosition + 10);

          // Split text to fit page width
          const textLines = doc.splitTextToSize(text, 160);

          textLines.forEach((textLine: string) => {
            doc.text(textLine, 25, yPosition);
            yPosition += 7;

            if (yPosition > 280) {
              doc.addPage();
              yPosition = 20;
            }
          });

          continue;
        }
      }

      // Handle simple HTML tables: <table>, <thead>/<tbody>, <tr>, <th>/<td>
      if (line.includes('<table')) {
        // Accumulate the table block across lines
        const tableLines: string[] = [line];
        while (i + 1 < contentLines.length && !contentLines[i + 1].includes('</table>')) {
          i++;
          tableLines.push(contentLines[i]);
        }
        if (i + 1 < contentLines.length) {
          i++;
          tableLines.push(contentLines[i]);
        }
        const tableHtml = tableLines.join('\n');

        // Basic parsing for rows and cells
        const rowRegex = /<tr>([\s\S]*?)<\/tr>/g;
        let rowMatch: RegExpExecArray | null;
        const rows: string[][] = [];
        while ((rowMatch = rowRegex.exec(tableHtml)) !== null) {
          const rowHtml = rowMatch[1];
          const cellRegex = /<(?:td|th)[^>]*>([\s\S]*?)<\/(?:td|th)>/g;
          const cells: string[] = [];
          let cellMatch: RegExpExecArray | null;
          while ((cellMatch = cellRegex.exec(rowHtml)) !== null) {
            const cellText = cellMatch[1].replace(/<[^>]*>/g, '');
            cells.push(cellText);
          }
          if (cells.length) rows.push(cells);
        }

        // Render table grid
        const colCount = rows.reduce((m, r) => Math.max(m, r.length), 0);
        const colWidth = Math.floor(170 / Math.max(1, colCount));
        const rowHeight = 8;

        // Draw header row with bold font if present
        rows.forEach((r, rIdx) => {
          // Page break if needed
          if (yPosition + rowHeight > 285) {
            doc.addPage();
            yPosition = 20;
          }
          r.forEach((cell, cIdx) => {
            const x = 20 + cIdx * colWidth;
            // Cell border
            doc.setDrawColor(200, 200, 200);
            doc.rect(x, yPosition - 6, colWidth, rowHeight);
            // Text
            if (rIdx === 0) {
              doc.setFont('helvetica', 'bold');
            } else {
              doc.setFont('helvetica', 'normal');
            }
            const textLines = doc.splitTextToSize(cell, colWidth - 2);
            doc.text(textLines, x + 1, yPosition);
          });
          yPosition += rowHeight;
        });

        // Reset font
        doc.setFont('helvetica', 'normal');
        continue;
      }

      // Handle regular text, including inline <code> and links expanded by markdown renderer
      if (line.trim() !== '') {
        let work = line;
        // Render inline code segments in monospace by replacing them with markers
        const segments: { text: string; type: 'code' | 'text' }[] = [];
        while (true) {
          const m = work.match(/<code>(.*?)<\/code>/);
          if (!m) {
            if (work) segments.push({ text: work.replace(/<[^>]*>/g, ''), type: 'text' });
            break;
          }
          const [full, inner] = m;
          const before = work.slice(0, m.index!);
          if (before) segments.push({ text: before.replace(/<[^>]*>/g, ''), type: 'text' });
          segments.push({ text: inner, type: 'code' });
          work = work.slice(m.index! + full.length);
        }

        // Now render segments respecting width and fonts
        const maxWidth = 170;
        let x = 20;
        // Split the line into wrapped pieces by measuring as we go
        doc.setFont('helvetica', 'normal');
        let currentLine = '';
        const flushLine = () => {
          if (currentLine.trim() !== '') {
            doc.text(currentLine, x, yPosition);
            yPosition += 7;
            currentLine = '';
          } else {
            // If nothing on line, still advance minimally
            yPosition += 5;
          }
        };

        segments.forEach(seg => {
          if (seg.type === 'text') {
            const words = seg.text.split(/\s+/);
            for (const w of words) {
              const candidate = (currentLine ? currentLine + ' ' : '') + w;
              if (doc.getTextWidth(candidate) > maxWidth) {
                flushLine();
                currentLine = w;
              } else {
                currentLine = candidate;
              }
            }
          } else {
            // flush current text line first
            if (currentLine) flushLine();
            // render code segment in a light box
            const codeLines = doc.splitTextToSize(seg.text, maxWidth - 4);
            // background
            if (yPosition + codeLines.length * 6 + 6 > 290) {
              doc.addPage();
              yPosition = 20;
            }
            doc.setFillColor(245, 245, 245);
            doc.setDrawColor(220, 220, 220);
            const h = codeLines.length * 6 + 6;
            doc.roundedRect(20, yPosition - 4, maxWidth, h, 2, 2, 'FD');
            doc.setFont('courier', 'normal');
            doc.text(codeLines, 22, yPosition);
            yPosition += h + 2;
            doc.setFont('helvetica', 'normal');
          }
        });

        if (currentLine) flushLine();
      } else {
        // Empty line - add some space
        yPosition += 6;
      }
    }

    return yPosition;
  }

  /**
   * Process plain markdown content line by line
   * @private
   */
  private addPlainMarkdownContent(doc: any, content: string, startY: number): number {
    let yPosition = startY;
    const contentLines = (content || '').split('\n');

    // Basic spacing rules
    const addSeparator = () => {
      doc.setDrawColor(220, 220, 220);
      doc.line(20, yPosition, 190, yPosition);
      yPosition += 6;
    };

    for (let i = 0; i < contentLines.length; i++) {
      const line = contentLines[i];

      // Check for page overflow
      if (yPosition > 280) {
        doc.addPage();
        yPosition = 20;
      }

      // Handle headers (# Header)
      if (line.match(/^#{1,6}\s/)) {
        const headerLevel = line.match(/^(#{1,6})\s/)?.[1].length || 1;
        const headerText = line.replace(/^#{1,6}\s/, '');

        // Top margin before new section
        yPosition += 4;
        if (yPosition > 280) { doc.addPage(); yPosition = 20; }

        // Set font size based on header level
        const fontSize = 16 - (headerLevel - 1) * 2; // h1=16, h2=14, h3=12, etc.
        doc.setFontSize(fontSize);
        doc.setFont("helvetica", "bold");
        doc.text(headerText, 20, yPosition);
        doc.setFontSize(12); // Reset to default
        doc.setFont("helvetica", "normal");

        yPosition += 8;

        // Separator after top-level headings
        if (headerLevel === 1 || headerLevel === 2) {
          addSeparator();
        }
        continue;
      }

      // Handle bold text (**text**)
      if (line.includes('**')) {
        // This is a simplified approach - a more robust solution would parse the line
        // and alternate between bold and normal text
        doc.setFont("helvetica", "bold");
      }

      // Handle list items with spacing rules
      if (line.match(/^\s*[-*+]\s/)) {
        // Slight spacing before the first item in a list block
        if (i === 0 || !contentLines[i - 1].match(/^\s*[-*+]\s/)) {
          yPosition += 2;
        }
        const listText = line.replace(/^\s*[-*+]\s/, '• ');
        const indentMatch = line.match(/^(\s*)/)?.[1].length || 0;
        const indent = 20 + (indentMatch * 5); // Indent based on leading spaces

        // Split text to fit page width with indentation
        const textLines = doc.splitTextToSize(listText, 170 - (indent - 20));

        textLines.forEach((textLine: string) => {
          doc.text(textLine, indent, yPosition);
          yPosition += 7;

          if (yPosition > 280) {
            doc.addPage();
            yPosition = 20;
          }
        });

        // Slight spacing after the last item in a list block
        if (i + 1 >= contentLines.length || !contentLines[i + 1].match(/^\s*[-*+]\s/)) {
          yPosition += 2;
        }

        continue;
      }

      // Handle regular text
      if (line.trim() !== '') {
        // Split text to fit page width
        const textLines = doc.splitTextToSize(line, 170);

        textLines.forEach((textLine: string) => {
          doc.text(textLine, 20, yPosition);
          yPosition += 7;

          if (yPosition > 280) {
            doc.addPage();
            yPosition = 20;
          }
        });
      } else {
        // Empty line - add some space
        yPosition += 5;
      }

      // Reset font to normal after each line
      doc.setFont("helvetica", "normal");
    }

    return yPosition;
  }

  /**
   * Renders a chart in the PDF document using the ChartConfig from chart-tool
   * @param doc - The PDF document
   * @param chartConfig - The chart configuration from ChartTool
   * @param title - The title of the chart section
   * @param yPosition - The y position to start rendering
   * @returns The new y position after rendering the chart
   */
  private renderChartInPdf(doc: any, chartConfig: ChartConfig, title: string, yPosition: number): number {
    try {
      // Add title for the chart
      doc.setFontSize(14);
      doc.setFont('helvetica', 'bold');
      doc.text(title || chartConfig.title || 'Chart Visualization', 20, yPosition);
      yPosition += 10;

      // Add subtitle if available
      if (chartConfig.subtitle) {
        doc.setFontSize(12);
        doc.setFont('helvetica', 'italic');
        doc.text(chartConfig.subtitle, 20, yPosition);
        yPosition += 8;
      }

      // Set back to normal text
      doc.setFontSize(10);
      doc.setFont('helvetica', 'normal');

      // Add chart type info
      const chartTypeName = chartConfig.chartType.charAt(0).toUpperCase() + chartConfig.chartType.slice(1);
      doc.text(`Chart Type: ${chartTypeName}`, 20, yPosition);
      yPosition += 10;

      // Create a placeholder for the chart
      doc.setDrawColor(200, 200, 200);
      doc.setFillColor(240, 240, 240);
      doc.roundedRect(20, yPosition, 170, 100, 3, 3, 'FD');

      // Add chart data as text
      yPosition += 10;
      doc.text('Chart Data:', 30, yPosition);
      yPosition += 10;

      // Handle different chart types appropriately
      if (chartConfig.chartType === CHART_TYPES.TABLE) {
        // For table charts
        // Type assertion for table chart
        const tableConfig = chartConfig as unknown as {
          chartType: 'table';
          columns?: Array<{ title?: string; field?: string; header?: string; accessorKey?: string; }>;
          data?: Array<Record<string, any>>;
        };

        if (tableConfig.columns && tableConfig.data) {
          // Display column headers
          let xPos = 40;
          tableConfig.columns.slice(0, 3).forEach((column: any) => {
            doc.setFont('helvetica', 'bold');
            doc.text(column.title || column.field || column.header || column.accessorKey || 'Column', xPos, yPosition);
            xPos += 40;
          });
          doc.setFont('helvetica', 'normal');
          yPosition += 7;

          // Display rows (limit to 5)
          tableConfig.data.slice(0, 5).forEach((row: any) => {
            xPos = 40;
            tableConfig.columns!.slice(0, 3).forEach((column: any) => {
              const field = column.field || column.title || column.accessorKey || '';
              const value = field && row[field] !== undefined ? String(row[field]) : '';
              doc.text(value.substring(0, 15), xPos, yPosition);
              xPos += 40;
            });
            yPosition += 7;
          });

          // If we have more rows, add ellipsis
          if (tableConfig.data.length > 5) {
            doc.text(`... and ${tableConfig.data.length - 5} more rows`, 40, yPosition);
            yPosition += 7;
          }
        }
      } else if (chartConfig.chartType === CHART_TYPES.FLOW) {
        // For flow charts
        // Type assertion for flow chart
        const flowConfig = chartConfig as unknown as {
          chartType: 'flow';
          nodes?: Array<{ id: string; data?: { label?: string }; }>;
          edges?: Array<{ source: string; target: string; label?: string; }>;
        };

        if (flowConfig.nodes) {
          doc.text('Nodes:', 40, yPosition);
          yPosition += 7;

          flowConfig.nodes.slice(0, 5).forEach((node: any) => {
            doc.text(`- ${node.data?.label || node.id || 'Node'}`, 50, yPosition);
            yPosition += 7;
          });

          if (flowConfig.nodes.length > 5) {
            doc.text(`... and ${flowConfig.nodes.length - 5} more nodes`, 50, yPosition);
            yPosition += 7;
          }

          if (flowConfig.edges && flowConfig.edges.length > 0) {
            yPosition += 3;
            doc.text('Connections:', 40, yPosition);
            yPosition += 7;

            flowConfig.edges.slice(0, 3).forEach((edge: any) => {
              doc.text(`- ${edge.source} → ${edge.target}${edge.label ? `: ${edge.label}` : ''}`, 50, yPosition);
              yPosition += 7;
            });

            if (flowConfig.edges.length > 3) {
              doc.text(`... and ${flowConfig.edges.length - 3} more connections`, 50, yPosition);
              yPosition += 7;
            }
          }
        }
      } else {
        // For standard charts (bar, line, pie, etc.)
        // Type assertion for standard charts
        const standardConfig = chartConfig as unknown as {
          chartType: string;
          data?: Array<Record<string, any>>;
        };

        if (standardConfig.data && Array.isArray(standardConfig.data)) {
          // Display data points
          standardConfig.data.slice(0, 5).forEach((item: any, index: number) => {
            let label = '';
            let value = '';

            if (standardConfig.chartType === CHART_TYPES.SCATTER || standardConfig.chartType === CHART_TYPES.BUBBLE) {
              label = item.name || `Point ${index + 1}`;
              value = `(x: ${item.x}, y: ${item.y}${item.z ? `, z: ${item.z}` : ''})`;
            } else {
              // For bar, line, pie, etc.
              label = item.name || Object.keys(item).find(k => k !== 'value') || `Item ${index + 1}`;
              value = item.value !== undefined ? String(item.value) :
                     String(Object.values(item).find(v => typeof v === 'number') || '');
            }

            doc.text(`${label}: ${value}`, 40, yPosition);
            yPosition += 7;
          });

          // If we have more data points, add ellipsis
          if (standardConfig.data.length > 5) {
            doc.text(`... and ${standardConfig.data.length - 5} more data points`, 40, yPosition);
            yPosition += 7;
          }
        }
      }

      // Add explanation if available
      if (chartConfig.explanation) {
        yPosition += 5;
        doc.setFont('helvetica', 'italic');
        doc.text('Explanation:', 30, yPosition);
        yPosition += 7;

        // Split explanation into multiple lines if needed
        const maxWidth = 150;
        const lines = doc.splitTextToSize(chartConfig.explanation, maxWidth);
        doc.text(lines, 40, yPosition);
        yPosition += lines.length * 7;
      }

      // Add some space after the chart
      yPosition += 15;

      return yPosition;
    } catch (error) {
      console.error('Error rendering chart in PDF:', error);

      // Add error message
      doc.text('Chart visualization could not be rendered due to an error.', 20, yPosition);
      yPosition += 10;

      return yPosition;
    }
  }
}

// Export a singleton instance
export const pdfGeneratorTool = new PdfGeneratorTool();

/**
 * Generate a PDF from markdown content
 * @param options - PDF generation options
 * @returns - The PDF generation result
 */
export async function generatePDF(options: {
  title: string;
  content: string;
  fileName?: string;
  category?: string;
  metadata?: Record<string, string>;
}): Promise<{
  success: boolean;
  fileUrl?: string;
  error?: string;
}> {
  try {
    // Create PDF content
    const pdfContent: PdfContent[] = [
      {
        title: options.title,
        content: options.content
      }
    ];

    // Generate PDF
    const result = await pdfGeneratorTool.generatePdf(pdfContent, {
      title: options.title,
      saveToByteStore: true,
      category: options.category || 'PMO',
      documentType: 'Requirements Specification',
      includeCover: true,
      includeToc: true,
      ...options.metadata
    });

    // Check if result is a SavePdfToByteStoreResult
    if (typeof result === 'object' && 'downloadUrl' in result && typeof result.downloadUrl === 'string') {
      return {
        success: true,
        fileUrl: result.downloadUrl
      };
    }

    return {
      success: false,
      error: 'Failed to save PDF to byte store'
    };
  } catch (error: any) {
    console.error('Error generating PDF:', error);
    return {
      success: false,
      error: error.message || 'Failed to generate PDF'
    };
  }
}

