export const runtime = 'nodejs';
export const maxDuration = 60;

import { NextRequest, NextResponse } from 'next/server';
import { saveTranscript, TranscriptMetadata } from '../../../../lib/utils/transcriptUtils';
import { TranscriptMessage } from '../../../../components/PMO/TranscriptPanel';
import {
  generateMeetingDocument,
  determineMeetingDocumentCategory,
  MeetingDocumentGenerationResult
} from '../../../../lib/tools/meetingDocumentGenerator';
import { createPMORecordFromForm, updatePMORecord } from '../../../../lib/firebase/pmoCollection';
import { getTeamIdFromAgentType } from '../../../../lib/utils/teamNameUtils';
import { getPMOAgentConfig } from '../../../../lib/agents/voice/pmoAgentVoiceConfig';

/**
 * Webhook endpoint for ElevenLabs agents to save meeting summaries
 * This endpoint is called by the voice agent when the conversation is ending
 * to save the transcript and optionally generate documents
 *
 * This replaces the need for workspace-level post-call webhooks by allowing
 * the agent to proactively save the meeting content before ending the call.
 */
export async function POST(request: NextRequest) {
  const startTime = Date.now();
  const EARLY_ACK_TIMEOUT = 45000; // 45 seconds - leave buffer for Vercel's 60s limit

  try {
    console.log('[SAVE_MEETING_WEBHOOK] Received save meeting summary request from voice agent');
    console.log('[SAVE_MEETING_WEBHOOK] Request headers:', Object.fromEntries(request.headers.entries()));

    // Verify webhook authentication
    const authHeader = request.headers.get('authorization');
    const expectedAuth = `Bearer ${process.env.ELEVENLABS_WEBHOOK_SECRET || 'pmo-webhook-secret-2024'}`;

    if (authHeader !== expectedAuth) {
      console.error('[SAVE_MEETING_WEBHOOK] Unauthorized webhook request');
      console.error('[SAVE_MEETING_WEBHOOK] Received auth:', authHeader);
      console.error('[SAVE_MEETING_WEBHOOK] Expected auth:', expectedAuth);
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Helper function to check if we should return early
    const shouldReturnEarly = () => {
      const elapsed = Date.now() - startTime;
      return elapsed > EARLY_ACK_TIMEOUT;
    };

    // Parse request body
    const body = await request.json();
    console.log('[SAVE_MEETING_WEBHOOK] Request body:', JSON.stringify(body, null, 2));

    // Extract data from ElevenLabs webhook format
    const {
      summary,
      action_items,
      document_title,
      generate_document = true,
      // Web search results from the meeting
      web_search_results = [],
      search_queries = [],
      research_findings = '',
      // ElevenLabs provides these automatically
      agent_id,
      conversation_id,
      user_id = process.env.NEXT_PUBLIC_SYS_ADMIN || '<EMAIL>', // Use system admin instead of 'system'
      conversation_history = [],
      // New parameters for proper agent and document handling
      agent_type,
      document_category
    } = body;

    console.log('[SAVE_MEETING_WEBHOOK] Processing meeting summary:', {
      agent_id,
      conversation_id,
      user_id,
      agent_type,
      document_category,
      summary_length: summary?.length || 0,
      has_action_items: !!action_items,
      generate_document,
      conversation_history_length: conversation_history?.length || 0,
      web_search_results_count: web_search_results?.length || 0,
      search_queries_count: search_queries?.length || 0,
      has_research_findings: !!research_findings
    });

    // Validate required data
    if (!summary) {
      console.error('[SAVE_MEETING_WEBHOOK] Missing required summary');
      return NextResponse.json({
        error: 'Missing required field: summary'
      }, { status: 400 });
    }

    // Use the full conversation history if available, otherwise create from summary
    let transcriptMessages: TranscriptMessage[] = [];

    if (conversation_history && conversation_history.length > 0) {
      console.log('[SAVE_MEETING_WEBHOOK] Using full conversation history with', conversation_history.length, 'messages');

      // Convert conversation history to transcript messages
      transcriptMessages = conversation_history.map((msg: any, index: number) => ({
        role: msg.role || (msg.speaker === 'user' ? 'user' : 'assistant'),
        content: msg.content || msg.message || '',
        timestamp: msg.timestamp ? new Date(msg.timestamp) : new Date(Date.now() - (conversation_history.length - index) * 30000)
      }));
    } else {
      console.log('[SAVE_MEETING_WEBHOOK] No conversation history available, creating from summary');

      // Fallback: Create transcript messages from the summary and action items
      transcriptMessages = [
        {
          role: 'assistant',
          content: `Meeting Summary Generated by Agent`,
          timestamp: new Date(Date.now() - 60000) // 1 minute ago
        },
        {
          role: 'assistant',
          content: `Summary: ${summary}`,
          timestamp: new Date(Date.now() - 30000) // 30 seconds ago
        }
      ];

      // Add action items if provided
      if (action_items && action_items.trim().length > 0) {
        transcriptMessages.push({
          role: 'assistant',
          content: `Action Items: ${action_items}`,
          timestamp: new Date(Date.now() - 45000) // 45 seconds ago
        });
      }

      // Add web search results and research findings if available
      if (search_queries && search_queries.length > 0) {
        transcriptMessages.push({
          role: 'assistant',
          content: `Web Search Queries Performed: ${search_queries.join(', ')}`,
          timestamp: new Date(Date.now() - 30000) // 30 seconds ago
        });
      }

      if (research_findings && research_findings.trim().length > 0) {
        transcriptMessages.push({
          role: 'assistant',
          content: `Research Findings: ${research_findings}`,
          timestamp: new Date(Date.now() - 15000) // 15 seconds ago
        });
      }

      // Add final summary message
      transcriptMessages.push({
        role: 'assistant',
        content: `Meeting documentation completed. Summary, action items, and research findings have been saved.`,
        timestamp: new Date()
      });
    }

    console.log('[SAVE_MEETING_WEBHOOK] Created transcript with', transcriptMessages.length, 'messages');

    // Extract metadata for transcript saving
    const agentName = 'PMO Agent'; // Default, could be enhanced to get from agent_id
    const agentType = 'PMO';
    const category = 'Meeting Transcript';
    const finalDocumentTitle = document_title || `Meeting Summary - ${agentName} - ${new Date().toLocaleDateString()}`;

    // Create transcript metadata
    const transcriptMetadata: TranscriptMetadata = {
      agentName,
      agentType,
      documentTitle: finalDocumentTitle,
      category,
      startTime: new Date(Date.now() - (transcriptMessages.length * 30000)), // Estimate start time
      endTime: new Date(),
      totalMessages: transcriptMessages.length,
      userMessages: transcriptMessages.filter(m => m.role === 'user').length,
      agentMessages: transcriptMessages.filter(m => m.role === 'assistant').length
    };

    console.log('[SAVE_MEETING_WEBHOOK] Saving transcript with metadata:', transcriptMetadata);

    // Save transcript using existing utility with timeout tracking
    console.log('[SAVE_MEETING_WEBHOOK] Starting transcript save process...');
    const saveStartTime = Date.now();

    // Use early-ack pattern: save PDF first, then handle KB upload separately
    const saveResult = await saveTranscript(transcriptMessages, {
      userId: user_id,
      agentId: agent_id || 'unknown',
      metadata: transcriptMetadata,
      uploadToKnowledgeBase: false, // Skip KB upload in main flow to avoid timeout
      forceUpload: false,
      onProgress: (step: string, message: string, progress?: number) => {
        const elapsed = Date.now() - saveStartTime;
        console.log(`[SAVE_MEETING_WEBHOOK] Progress (${elapsed}ms) - ${step}: ${message} (${progress || 0}%)`);
      }
    });

    const saveElapsed = Date.now() - saveStartTime;
    console.log(`[SAVE_MEETING_WEBHOOK] PDF generation completed in ${saveElapsed}ms`);

    if (!saveResult.success) {
      console.error('[SAVE_MEETING_WEBHOOK] Failed to save transcript:', saveResult.error);
      return NextResponse.json({
        error: 'Failed to save transcript',
        details: saveResult.error
      }, { status: 500 });
    }

    // Check if we should return early to avoid timeout
    if (shouldReturnEarly()) {
      console.log('[SAVE_MEETING_WEBHOOK] Returning early to avoid timeout, KB upload will continue in background');

      // Start background KB upload (fire and forget)
      if (saveResult.pdfUrl && (agent_id || 'unknown') !== 'unknown') {
        setImmediate(async () => {
          try {
            console.log('[SAVE_MEETING_WEBHOOK] Starting background KB upload...');
            // TODO: Implement background KB upload here
            console.log('[SAVE_MEETING_WEBHOOK] Background KB upload completed');
          } catch (error) {
            console.error('[SAVE_MEETING_WEBHOOK] Background KB upload failed:', error);
          }
        });
      }

      return NextResponse.json({
        success: true,
        message: 'Meeting summary saved successfully',
        pdfUrl: saveResult.pdfUrl,
        fileName: saveResult.fileName,
        note: 'Knowledge base upload continuing in background'
      });
    }

    console.log('[SAVE_MEETING_WEBHOOK] Transcript saved successfully:', {
      pdfUrl: saveResult.pdfUrl,
      fileName: saveResult.fileName,
      knowledgeBaseId: saveResult.knowledgeBaseId
    });

    // Handle document generation if requested
    let documentGenerationResult: MeetingDocumentGenerationResult | null = null;

    if (generate_document) {
      try {
        console.log('[SAVE_MEETING_WEBHOOK] Document generation requested:', {
          title: finalDocumentTitle,
          summary_length: summary.length
        });

        // Create PMO record first
        let pmoId: string | null = null;
        try {
          console.log('[SAVE_MEETING_WEBHOOK] Creating PMO record...');
          pmoId = await createPMORecordFromForm(user_id, {
            title: finalDocumentTitle,
            description: summary,
            priority: 'Medium',
            category: 'Meeting Documents'
          });

          console.log('[SAVE_MEETING_WEBHOOK] PMO record created:', pmoId);
        } catch (pmoError) {
          console.error('[SAVE_MEETING_WEBHOOK] Error creating PMO record:', pmoError);
        }

        // Use provided document_category or determine from meeting context
        const finalDocumentCategory = document_category || determineMeetingDocumentCategory(
          [],
          summary + (action_items || '')
        );

        // Map agent_type to proper AgenticTeamId enum value
        let mappedAgentId = 'unknown';
        let finalAgentType: string | null = null;

        // Use provided agent_type first, then try to extract from agent_id
        if (agent_type) {
          finalAgentType = agent_type;
          console.log('[SAVE_MEETING_WEBHOOK] Using provided agent_type:', agent_type);
        } else if (agent_id && agent_id.includes('-pmo-')) {
          const parts = agent_id.split('-pmo-');
          if (parts.length > 1) {
            finalAgentType = parts[1]; // Extract the agent type (e.g., "Marketing")
            console.log('[SAVE_MEETING_WEBHOOK] Extracted agent_type from agent_id:', finalAgentType);
          }
        } else if (agent_id) {
          // Fallback: infer from agent_id content
          const agentIdLower = agent_id.toLowerCase();
          if (agentIdLower.includes('marketing')) {
            finalAgentType = 'Marketing';
          } else if (agentIdLower.includes('research')) {
            finalAgentType = 'Research';
          } else if (agentIdLower.includes('sales')) {
            finalAgentType = 'Sales';
          } else if (agentIdLower.includes('software') || agentIdLower.includes('design')) {
            finalAgentType = 'SoftwareDesign';
          } else if (agentIdLower.includes('business')) {
            finalAgentType = 'BusinessAnalysis';
          } else if (agentIdLower.includes('investigative')) {
            finalAgentType = 'InvestigativeResearch';
          } else if (agentIdLower.includes('codebase')) {
            finalAgentType = 'CodebaseDocumentation';
          } else if (agentIdLower.includes('documentation')) {
            finalAgentType = 'DocumentationGeneration';
          }
          console.log('[SAVE_MEETING_WEBHOOK] Inferred agent_type from agent_id:', finalAgentType);
        }

        // Map the agent type to the AgenticTeamId enum value
        if (finalAgentType) {
          const agentConfig = getPMOAgentConfig(finalAgentType);
          if (agentConfig && agentConfig.agentId) {
            mappedAgentId = agentConfig.agentId; // This will be 'Ag001', 'Ag002', etc.
          } else {
            // Fallback to direct mapping function
            const teamId = getTeamIdFromAgentType(finalAgentType);
            if (teamId) {
              mappedAgentId = teamId;
            }
          }
        }

        console.log('[SAVE_MEETING_WEBHOOK] Agent ID mapping:', {
          original: agent_id,
          extractedAgentType: finalAgentType,
          mapped: mappedAgentId,
          isValidTeamId: mappedAgentId.startsWith('Ag'),
          providedCategory: document_category,
          finalCategory: finalDocumentCategory
        });

        // Generate the meeting document WITHOUT PMO ID prefix (as requested)
        documentGenerationResult = await generateMeetingDocument({
          title: finalDocumentTitle,
          category: finalDocumentCategory,
          meetingTranscript: transcriptMessages,
          agentId: mappedAgentId,
          userId: user_id,
          webSearchResults: web_search_results, // Include web search results from the meeting
          additionalContext: `Meeting Summary: ${summary}${action_items ? `\n\nAction Items: ${action_items}` : ''}${research_findings ? `\n\nResearch Findings: ${research_findings}` : ''}${search_queries.length > 0 ? `\n\nSearch Queries: ${search_queries.join(', ')}` : ''}`,
          pmoId: pmoId || undefined,
          onProgress: (step: string, message: string, progress?: number) => {
            console.log(`[SAVE_MEETING_WEBHOOK] Document Generation - ${step}: ${message} (${progress || 0}%)`);
          }
        });

        if (documentGenerationResult.success) {
          console.log('[SAVE_MEETING_WEBHOOK] Document generated successfully:', {
            documentId: documentGenerationResult.documentId,
            downloadUrl: documentGenerationResult.downloadUrl,
            knowledgeBaseId: documentGenerationResult.knowledgeBaseId
          });

          // Update PMO record with document details and team assignment
          if (pmoId) {
            try {
              const updateData: any = {
                status: 'Completed',
                summary: `Document generated successfully. Knowledge Base ID: ${documentGenerationResult.knowledgeBaseId || 'N/A'}`
              };

              // Add agent ID if we have a valid one
              if (mappedAgentId !== 'unknown') {
                updateData.agentIds = [mappedAgentId];
              }

              await updatePMORecord(user_id, pmoId, updateData);
              console.log('[SAVE_MEETING_WEBHOOK] PMO record updated with document details');
            } catch (updateError) {
              console.error('[SAVE_MEETING_WEBHOOK] Error updating PMO record:', updateError);
            }
          }
        } else {
          console.error('[SAVE_MEETING_WEBHOOK] Document generation failed:', documentGenerationResult.error);

          // Update PMO record to reflect failure
          if (pmoId) {
            try {
              await updatePMORecord(user_id, pmoId, {
                status: 'Cancelled',
                summary: `Document generation failed: ${documentGenerationResult.error || 'Unknown error'}`
              });
            } catch (updateError) {
              console.error('[SAVE_MEETING_WEBHOOK] Error updating PMO record with failure:', updateError);
            }
          }
        }

      } catch (docGenError) {
        console.error('[SAVE_MEETING_WEBHOOK] Error during document generation:', docGenError);
        documentGenerationResult = {
          success: false,
          error: docGenError instanceof Error ? docGenError.message : 'Unknown error during document generation'
        };
      }
    }

    // Return success response to the agent
    const response = {
      success: true,
      message: 'Meeting summary saved successfully',
      results: {
        transcript: {
          saved: true,
          pdfUrl: saveResult.pdfUrl,
          fileName: saveResult.fileName,
          knowledgeBaseId: saveResult.knowledgeBaseId,
          messageCount: transcriptMessages.length
        },
        documentGeneration: documentGenerationResult
      }
    };

    console.log('[SAVE_MEETING_WEBHOOK] Sending success response:', response);
    return NextResponse.json(response);

  } catch (error) {
    console.error('[SAVE_MEETING_WEBHOOK] Error processing save meeting summary webhook:', error);
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
